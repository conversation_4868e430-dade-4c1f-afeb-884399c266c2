<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>BookingBear Availability Search - Development</title>
  <style>
    body {
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      background-color: #f5f5f5;
      padding: 20px;
      margin: 0;
    }

    h1 {
      color: #003B5C;
      text-align: center;
      margin-bottom: 30px;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    .custom-theme {
      --bb-color-background: #FFF0F5; /* Light pink instead of light blue */
      --bb-color-primary: #8B008B; /* Dark magenta instead of dark blue */
      --bb-color-accent-primary: #FF4500; /* Orange-red instead of green */
      --bb-color-accent-secondary: #4B0082; /* Indigo instead of yellow */
      --bb-color-accent-cta: #00CED1; /* Turquoise instead of pink */
    }

    .dev-info {
      background-color: #e6f7ff;
      border: 1px solid #91d5ff;
      border-radius: 4px;
      padding: 15px;
      margin-bottom: 20px;
    }

    .dev-info h3 {
      margin-top: 0;
      color: #0050b3;
    }

    .dev-info p {
      margin-bottom: 5px;
    }

    .dev-info code {
      background-color: #f0f0f0;
      padding: 2px 4px;
      border-radius: 3px;
      font-family: monospace;
    }

    .dev-links {
      margin-top: 10px;
    }

    .dev-links a {
      display: inline-block;
      margin-right: 15px;
      color: #1890ff;
      text-decoration: none;
    }

    .dev-links a:hover {
      text-decoration: underline;
    }

    .dev-status {
      margin-top: 15px;
      padding: 10px;
      background-color: #f0f9ff;
      border: 1px solid #d0e8ff;
      border-radius: 4px;
    }

    .dev-status p {
      margin: 5px 0;
    }

    #hmr-status {
      font-weight: bold;
      color: #1890ff;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>BookingBear Availability Search - Development</h1>

    <div class="dev-info">
      <h3>Development Mode</h3>
      <p>This is the development version of the component. Changes to the source code will be <strong>automatically reflected here</strong> without needing to rebuild or restart.</p>
      <p>You can also test the component in the standalone test.html file:</p>
      <div class="dev-links">
        <a href="/public/test.html" target="_blank">Open test.html</a>
      </div>
      <div class="dev-status">
        <p><strong>HMR Status:</strong> <span id="hmr-status">Connected</span></p>
        <p><small>Last updated: <span id="last-update-time">${new Date().toLocaleTimeString()}</span></small></p>
      </div>
    </div>

    <script>
      // Update the last update time when the page loads or HMR updates
      document.getElementById('last-update-time').textContent = new Date().toLocaleTimeString();

      // Listen for HMR events
      window.addEventListener('message', (event) => {
        if (event.data && event.data.type === 'vite:beforeUpdate') {
          document.getElementById('hmr-status').textContent = 'Updating...';
          document.getElementById('hmr-status').style.color = '#e6a700';
        }
        if (event.data && event.data.type === 'vite:afterUpdate') {
          document.getElementById('hmr-status').textContent = 'Updated';
          document.getElementById('hmr-status').style.color = '#4caf50';
          document.getElementById('last-update-time').textContent = new Date().toLocaleTimeString();

          // Reset status after 2 seconds
          setTimeout(() => {
            document.getElementById('hmr-status').textContent = 'Connected';
            document.getElementById('hmr-status').style.color = '';
          }, 2000);
        }
        if (event.data && event.data.type === 'vite:error') {
          document.getElementById('hmr-status').textContent = 'Error';
          document.getElementById('hmr-status').style.color = '#f44336';
        }
      });

      // Helper function to debug CSS variables
      function debugCssVariables() {
        const customThemeEl = document.querySelector('.custom-theme');
        const natureThemeEl = document.querySelector('availability-search[style*="--bb-color"]');

        console.log('Custom Theme CSS Variables:');
        if (customThemeEl) {
          const style = getComputedStyle(customThemeEl);
          console.log('--bb-color-background:', style.getPropertyValue('--bb-color-background'));
          console.log('--bb-color-primary:', style.getPropertyValue('--bb-color-primary'));
          console.log('--bb-color-accent-primary:', style.getPropertyValue('--bb-color-accent-primary'));
          console.log('--bb-color-accent-secondary:', style.getPropertyValue('--bb-color-accent-secondary'));
          console.log('--bb-color-accent-cta:', style.getPropertyValue('--bb-color-accent-cta'));
        }

        console.log('Nature Theme CSS Variables:');
        if (natureThemeEl) {
          const style = getComputedStyle(natureThemeEl);
          console.log('--bb-color-background:', style.getPropertyValue('--bb-color-background'));
          console.log('--bb-color-primary:', style.getPropertyValue('--bb-color-primary'));
          console.log('--bb-color-accent-primary:', style.getPropertyValue('--bb-color-accent-primary'));
          console.log('--bb-color-accent-secondary:', style.getPropertyValue('--bb-color-accent-secondary'));
          console.log('--bb-color-accent-cta:', style.getPropertyValue('--bb-color-accent-cta'));
        }
      }

      // Run the debug function after the component is loaded
      setTimeout(debugCssVariables, 1000);
    </script>

    <h2>Default Theme</h2>
    <availability-search
     site-id="J0fU9kdfFMXQ"
	 group-id="1"
    >
    </availability-search>

    <h2>Custom Theme (Magenta & Pink)</h2>
    <availability-search
      class="custom-theme"
     site-id="J0fU9kdfFMXQ"
	>
    </availability-search>

    <h2>Nature Theme (Green & Brown)</h2>
    <availability-search
      style="
        --bb-color-background: #F5F5DC; /* Beige */
        --bb-color-primary: #2E8B57; /* Sea Green */
        --bb-color-accent-primary: #8B4513; /* Saddle Brown */
        --bb-color-accent-secondary: #DAA520; /* Goldenrod */
        --bb-color-accent-cta: #556B2F; /* Dark Olive Green */
      "
     site-id="J0fU9kdfFMXQ"
      accommodations-endpoint="https://laravel-app.lndo.site/api/accommodations"
      availability-endpoint="https://laravel-app.lndo.site/api/accommodations/{id}/check-availability">
    </availability-search>
  </div>

  <!-- Use the development entry point for better DevTools integration -->
  <script type="module" src="/src/dev-main.js"></script>
</body>
</html>
