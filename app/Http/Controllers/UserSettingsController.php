<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class UserSettingsController extends Controller
{
    /**
     * Display the user settings page.
     */
    public function index()
    {
        $user = Auth::user();
        $bookingTerms = $user->getSetting('booking_terms_and_conditions', '');

        // Get company logo URL if it exists
        $companyLogo = null;
        $logoSetting = $user->settings()->where('key', 'company_logo')->first();
        if ($logoSetting && $logoSetting->hasMedia('company_logo')) {
            $companyLogo = $logoSetting->getFirstMediaUrl('company_logo');
        }

        // Get discount settings
        $discountSettings = [
            'discount_application_strategy' => $user->getSetting('discount_application_strategy', 'stack'),
        ];

        return Inertia::render('Settings/Index', [
            'bookingTerms' => $bookingTerms,
            'companyLogo' => $companyLogo,
            'discountSettings' => $discountSettings,
        ]);
    }

    /**
     * Update all settings.
     *
     * This is a generic method that can handle multiple settings at once.
     */
    public function updateSettings(Request $request)
    {
        $validated = $request->validate([
            'settings.bookingTerms' => ['nullable', 'string'],
            'settings.companyLogo' => ['nullable', 'image', 'mimes:jpeg,png,jpg,svg,webp', 'max:2048'],
            'settings.removeLogo' => ['nullable', 'boolean'],
            'discount_application_strategy' => ['nullable', 'string', 'in:stack,single_best'],
            // Add validation rules for other settings as they are added
        ]);

        $user = Auth::user();
        $settingsMap = [
            'bookingTerms' => ['key' => 'booking_terms_and_conditions', 'type' => 'string'],
            // Map frontend keys to database keys and their types
        ];

        try {
            // Handle logo removal first
            if ($request->input('settings.removeLogo', false)) {
                $logoSetting = $user->settings()->where('key', 'company_logo')->first();
                if ($logoSetting) {
                    $logoSetting->clearMediaCollection('company_logo');
                    $logoSetting->delete();
                    
                    return response()->json([
                        'success' => 'Logo removed successfully.',
                        'companyLogo' => null
                    ]);
                }
                
                return response()->json([
                    'success' => 'No logo to remove.'
                ]);
            }
            
            // Handle file uploads
            if ($request->hasFile('settings.companyLogo')) {
                $setting = $user->settings()->firstOrCreate(
                    ['key' => 'company_logo'],
                    ['value' => '', 'type' => 'media']
                );
                
                // Clear existing media and add the new file
                $setting->clearMediaCollection('company_logo');
                
                // Add the new file with a sanitized name
                $media = $setting->addMediaFromRequest('settings.companyLogo')
                    ->sanitizingFileName(function($fileName) {
                        return strtolower(str_replace(['#', '/', '\\', ' '], '-', $fileName));
                    })
                    ->toMediaCollection('company_logo');
                
                // Get the full public URL for the response
                $logoUrl = $media->getFullUrl();
                
                return response()->json([
                    'success' => 'Logo updated successfully.',
                    'companyLogo' => $logoUrl
                ]);
            }
            
            // Handle other settings
            if (isset($validated['settings'])) {
                foreach ($validated['settings'] as $key => $value) {
                    if (!isset($settingsMap[$key])) {
                        continue;
                    }

                    $setting = $settingsMap[$key];
                    $user->setSetting($setting['key'], $value, $setting['type']);
                }
            }

            // Handle discount settings
            if (isset($validated['discount_application_strategy'])) {
                $user->setSetting('discount_application_strategy', $validated['discount_application_strategy'], 'string');
            }

            return response()->json([
                'success' => 'Settings updated successfully.'
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating settings: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'exception' => $e,
            ]);

            return redirect()->route('settings.index')
                ->with('error', 'An error occurred while updating the settings.');
        }
    }

    /**
     * Update the booking terms and conditions.
     *
     * @deprecated Use updateSettings instead
     */
    public function updateBookingTerms(Request $request)
    {
        $validated = $request->validate([
            'terms' => 'nullable|string',
        ]);

        $user = Auth::user();

        try {
            // Save the booking terms and conditions
            $user->setSetting('booking_terms_and_conditions', $validated['terms']);

            return redirect()->route('settings.index')
                ->with('success', 'Booking terms and conditions updated successfully.');
        } catch (\Exception $e) {
            Log::error('Error updating booking terms: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'exception' => $e,
            ]);

            return redirect()->route('settings.index')
                ->with('error', 'An error occurred while updating the booking terms and conditions.');
        }
    }
}
