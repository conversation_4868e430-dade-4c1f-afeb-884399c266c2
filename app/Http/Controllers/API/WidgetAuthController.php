<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Site;
use App\Models\WidgetAuthLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use OpenApi\Annotations as OA;

/**
 * @OA\Tag(
 *     name="Widget Authentication",
 *     description="API endpoints for widget-based authentication using site ID and domain verification"
 * )
 */
class WidgetAuthController extends Controller
{
    /**
	 * @OA\Post(
	 *     path="/api/widget/authenticate",
	 *     tags={"Widget Authentication"},
	 *     summary="Authenticate widget access using site ID and domain",
	 *     @OA\RequestBody(
	 *         required=true,
	 *         @OA\JsonContent(
	 *             required={"site_id", "domain"},
	 *             @OA\Property(property="site_id", type="string", example="12345"),
	 *             @OA\Property(property="domain", type="string", example="example.com")
	 *         )
	 *     ),
	 *     @OA\Response(
	 *         response=200,
	 *         description="Authentication successful",
	 *         @OA\JsonContent(
	 *             @OA\Property(property="message", type="string", example="Authentication successful"),
	 *             @OA\Property(property="token", type="string", example="your_generated_token")
	 *         )
	 *     ),
	 *     @OA\Response(
	 *         response=401,
	 *         description="Authentication failed",
	 *         @OA\JsonContent(
	 *             @OA\Property(property="message", type="string", example="Authentication failed: Invalid site ID")
	 *         )
	 *     ),
	 *     @OA\Response(
	 *         response=422,
	 *         description="Validation error",
	 *         @OA\JsonContent(
	 *             @OA\Property(property="message", type="string", example="Validation failed"),
	 *             @OA\Property(property="errors", type="object")
	 *         )
	 *    )
	 *)
	*/
    public function authenticate(Request $request)
    {
        // Validate the request
        $validator = Validator::make($request->all(), [
            'site_id' => 'required|string',
            'domain' => 'required|string',
        ]);

        if ($validator->fails()) {

            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Get the site ID and domain from the request
        $siteId = $request->input('site_id');
        $domain = $request->input('domain');

        // Log the authentication attempt
        Log::info('Widget authentication attempt', [
            'site_id' => $siteId,
            'domain' => $domain,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        // Find the site by site ID
        $site = Site::where('site_id', $siteId)
            ->where('published', true)
            ->first();

        if (!$site) {
            $failureReason = 'Site ID not found or inactive';

            Log::warning('Widget authentication failed: ' . $failureReason, [
                'site_id' => $siteId,
                'domain' => $domain,
            ]);

            return response()->json([
                'message' => 'Authentication failed: Invalid site ID'
            ], 401);
        }

        // Verify the domain
        // We'll do a simple check to see if the domain contains the registered domain
        // This allows for subdomains and different protocols (http/https)
        $registeredDomain = $site->domain;
        $domainMatch = Str::contains($domain, $registeredDomain) ||
                       Str::contains($registeredDomain, $domain);

        // Allow localhost and .local domains in local environment
        $isLocalDevelopment = $domain === 'localhost' ||
                             Str::contains($domain, '.local') ||
                             app()->environment('local');

        if (!$domainMatch && !$isLocalDevelopment) {
            $failureReason = 'Domain mismatch';

            Log::warning('Widget authentication failed: ' . $failureReason, [
                'site_id' => $siteId,
                'registered_domain' => $registeredDomain,
                'request_domain' => $domain,
            ]);

            return response()->json([
                'message' => 'Authentication failed: Domain not authorized'
            ], 401);
        }

        // Update the last used timestamp
        $site->update(['last_used_at' => now()]);

        // Generate a token for the site owner
        $user = $site->user;
        
        // Use a consistent token name for this site
        $tokenName = 'widget-auth-'.$site->site_id;
        
        // Check if a token already exists for this site
        $existingToken = DB::table('personal_access_tokens')
            ->where('tokenable_id', $user->id)
            ->where('tokenable_type', get_class($user))
            ->where('name', $tokenName)
            ->first();

        if ($existingToken) {
            // Token exists, but we can't retrieve its plain text value
            // We need to get it from our own storage

            // Check if we have the token stored in the site model
            if (empty($site->api_key)) {
                // If we don't have it stored, we need to create a new one
                // First, delete the old token
                $user->tokens()->where('id', $existingToken->id)->delete();

                // Create a new non-expiring token
                $plainTextToken = $user->createToken($tokenName, ['widget-access'])->plainTextToken;

                // Store the token in the site model for future use
                $site->update(['api_key' => $plainTextToken]);

                $token = $plainTextToken;
            } else {
                // Use the stored token
                $token = $site->api_key;
            }
        } else {
            // No token exists, create a new non-expiring token
            $plainTextToken = $user->createToken($tokenName, ['widget-access'])->plainTextToken;

            // Store the token in the site model for future use
            $site->update(['api_key' => $plainTextToken]);

            $token = $plainTextToken;
        }

        return response()->json([
            'message' => 'Authentication successful',
            'token' => $token,
        ]);
    }
}
