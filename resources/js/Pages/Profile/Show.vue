<script setup>
import DashboardLayout from '@/Layouts/DashboardLayout.vue';
import Card from '@/Components/Card.vue';
import DeleteUserForm from '@/Pages/Profile/Partials/DeleteUserForm.vue';
import LogoutOtherBrowserSessionsForm from '@/Pages/Profile/Partials/LogoutOtherBrowserSessionsForm.vue';

import TwoFactorAuthenticationForm from '@/Pages/Profile/Partials/TwoFactorAuthenticationForm.vue';
import UpdatePasswordForm from '@/Pages/Profile/Partials/UpdatePasswordForm.vue';
import UpdateProfileInformationForm from '@/Pages/Profile/Partials/UpdateProfileInformationForm.vue';


defineProps({
    confirmsTwoFactorAuthentication: Boolean,
    sessions: Array,
    settings: Object,
});
</script>

<template>
    <DashboardLayout title="Profile">
        <template #header>
            <h2 class="font-semibold text-xl text-primary leading-tight">
                Profile
            </h2>
        </template>

        <div class="py-6 px-4 sm:px-6 lg:px-8 space-y-6">
			<div v-if="$page.props.jetstream.canUpdateProfileInformation">
				<Card title="Profile Information" shadow="sm">
					<UpdateProfileInformationForm :user="$page.props.auth.user" />
				</Card>
			</div>

			<div v-if="$page.props.jetstream.canUpdatePassword">
				<Card title="Update Password" shadow="sm">
					<UpdatePasswordForm />
				</Card>
			</div>

			<div v-if="$page.props.jetstream.canManageTwoFactorAuthentication">
				<Card title="Two Factor Authentication" shadow="sm">
					<TwoFactorAuthenticationForm
						:requires-confirmation="confirmsTwoFactorAuthentication"
					/>
				</Card>
			</div>

			<div>
				<Card title="Browser Sessions" shadow="sm">
					<LogoutOtherBrowserSessionsForm :sessions="sessions" />
				</Card>
			</div>

			<div v-if="$page.props.jetstream.hasAccountDeletionFeatures">
				<Card title="Delete Account" shadow="sm">
					<DeleteUserForm />
				</Card>
			</div>


        </div>
    </DashboardLayout>
</template>
