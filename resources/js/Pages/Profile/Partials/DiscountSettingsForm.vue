<template>
  <div class="space-y-6">
    <div>
      <InputLabel for="discount_application_strategy" value="Discount Application Strategy" />
      <select id="discount_application_strategy" v-model="form.discount_application_strategy" class="mt-1 block w-full p-3 border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-accent-primary focus:border-accent-primary dark:focus:ring-accent-primary dark:focus:border-accent-primary sm:text-sm">
        <option value="stack">Stack Discounts</option>
        <option value="single_best">Apply Single Best Discount</option>
      </select>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        Choose how discounts are applied when multiple are valid.
      </p>
      <InputError :message="form.errors.discount_application_strategy" class="mt-2" />
    </div>
    <div class="flex items-center justify-end">
      <PrimaryButton :disabled="form.processing" :loading="form.processing" @click="submit">Save Discount Settings</PrimaryButton>
    </div>
  </div>
</template>

<script setup>
import { useForm } from '@inertiajs/vue3';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';

const props = defineProps({
  settings: Object,
});

const form = useForm({
  discount_application_strategy: props.settings?.discount_application_strategy || 'stack',
});

const submit = () => {
  form.post(route('user-settings.update'), {
    preserveScroll: true,
  });
};
</script>
