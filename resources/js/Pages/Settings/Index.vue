<script setup>
import { ref, onMounted } from 'vue';
import { Head, useForm, usePage, router } from '@inertiajs/vue3';
import axios from 'axios';
import DashboardLayout from '@/Layouts/DashboardLayout.vue';
import Card from '@/Components/Card.vue';
import Alert from '@/Components/Alert.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import InputLabel from '@/Components/InputLabel.vue';
import DiscountSettingsForm from '@/Pages/Settings/Partials/DiscountSettingsForm.vue';
import { QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css';

const props = defineProps({
    bookingTerms: {
        type: String,
        default: '',
    },
    companyLogo: {
        type: String,
        default: '',
    },
    discountSettings: {
        type: Object,
        default: () => ({}),
    },
    // Add more settings props here as needed
});

const page = usePage();
const flashSuccess = ref(page.props.flash?.success);
const flashError = ref(page.props.flash?.error);

// Create a form with all settings
const formData = useForm({
    settings: {
        bookingTerms: props.bookingTerms,
        companyLogo: null, // Will be handled separately
        // Add more settings here as needed
    }
});

const fileInput = ref(null);
const companyLogo = ref(props.companyLogo);
const isUploading = ref(false);

// Handle logo file selection
const handleLogoChange = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Check file size (2MB max)
    if (file.size > 2 * 1024 * 1024) {
        formData.setError('settings.companyLogo', 'File size should be less than 2MB');
        return;
    }

    // Check file type
    const validTypes = ['image/jpeg', 'image/png', 'image/svg+xml', 'image/webp'];
    if (!validTypes.includes(file.type)) {
        formData.setError('settings.companyLogo', 'Invalid file type. Please upload a JPEG, PNG, SVG, or WebP image.');
        return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
        companyLogo.value = e.target.result;
        formData.settings.companyLogo = file;
        submit(); // Auto-submit on file select
    };
    reader.readAsDataURL(file);
};

// Remove logo
const removeLogo = async () => {
    try {
        isUploading.value = true;
        await axios.post(route('settings.update'), {
            data: {
                settings: {
                    removeLogo: true
                }
            },
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        });
        
        companyLogo.value = null;
        formData.settings.companyLogo = null;
        if (fileInput.value) {
            fileInput.value.value = '';
        }
    } catch (error) {
        console.error('Error removing logo:', error);
    } finally {
        isUploading.value = false;
    }
};

onMounted(() => {
    formData.settings.bookingTerms = props.bookingTerms;
    formData.settings.companyLogo = props.companyLogo;
});

// Submit the form
const submit = async () => {
    try {
        isUploading.value = true;
        
        // Create form data for file uploads
        const formDataToSend = new FormData();
        
        // Add all settings to form data
        Object.entries(formData.settings).forEach(([key, value]) => {
            // Skip if value is null or undefined
            if (value === null || value === undefined) return;
            
            // Handle file uploads specially
            if (key === 'companyLogo' && value instanceof File) {
                formDataToSend.append('settings[companyLogo]', value);
            } else if (key !== 'companyLogo') {
                // Only add non-logo settings to the form data
                formDataToSend.append(`settings[${key}]`, value);
            }
        });
        
        // Submit the form
        const response = await axios.post(route('settings.update'), formDataToSend, {
            headers: {
                'Content-Type': 'multipart/form-data',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            },
            onUploadProgress: (progressEvent) => {
                // Handle upload progress if needed
                const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                console.log(`Upload progress: ${progress}%`);
            }
        });
        
        // Show success message
        if (response.data.success) {
            flashSuccess.value = response.data.success;
            
            // Update the logo URL if it was included in the response
            if (response.data.companyLogo !== undefined) {
                companyLogo.value = response.data.companyLogo;
            }
            
            // Reset the file input to allow re-uploading the same file
            if (formData.settings.companyLogo instanceof File) {
                formData.settings.companyLogo = null;
                if (fileInput.value) {
                    fileInput.value.value = '';
                }
            }
        }
    } catch (error) {
        console.error('Error updating settings:', error);
        
        // Handle validation errors
        if (error.response && error.response.data.errors) {
            formData.errors = error.response.data.errors;
        } else {
            flashError.value = error.response?.data?.message || 'An error occurred while updating settings.';
        }
    } finally {
        isUploading.value = false;
    }
};
</script>

<template>
    <Head title="Settings" />

    <DashboardLayout>
        <template #header>
            <h2 class="font-semibold text-xl text-primary leading-tight">
                Settings
            </h2>
        </template>

        <div class="py-6 px-4 sm:px-6 lg:px-8">
            <!-- Flash Messages -->
            <Alert v-if="flashError" type="danger" class="mb-6" dismissible>
                {{ flashError }}
            </Alert>
            <Alert v-if="flashSuccess" type="success" class="mb-6" dismissible>
                {{ flashSuccess }}
            </Alert>

            <Card title="Company Information" class="mb-6">
                <div class="space-y-6">
                    <!-- Logo Upload -->
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700">Company Logo</label>
                        <p class="mt-1 text-sm text-gray-500">Upload your company logo to be displayed on your booking pages.</p>
                        
                        <div class="mt-2">
                            <!-- Logo Preview -->
                            <div v-if="companyLogo" class="flex items-center space-x-4 mb-4">
                                <div class="flex-shrink-0">
                                    <img :src="companyLogo" class="h-24 w-24 rounded-md object-cover border border-gray-200 p-1" alt="Company Logo">
                                </div>
                                <div class="flex space-x-3">
                                    <button 
                                        type="button"
                                        @click="$refs.fileInput.click()"
                                        class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                                        :disabled="isUploading"
                                    >
                                        <svg v-if="isUploading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        <span v-else>Change</span>
                                    </button>
                                    <button 
                                        type="button"
                                        @click="removeLogo"
                                        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                                        :disabled="isUploading"
                                    >
                                        Remove
                                    </button>
                                </div>
                            </div>
                            
                            <!-- Upload Button (when no logo) -->
                            <div v-else>
                                <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
                                    <div class="space-y-1 text-center">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                            <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                        </svg>
                                        <div class="flex text-sm text-gray-600">
                                            <label class="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500">
                                                <span>Upload a file</span>
                                                <input 
                                                    id="company-logo" 
                                                    ref="fileInput"
                                                    name="company-logo" 
                                                    type="file" 
                                                    class="sr-only" 
                                                    accept="image/png, image/jpeg, image/svg+xml, image/webp"
                                                    @change="handleLogoChange"
                                                    :disabled="isUploading"
                                                >
                                            </label>
                                            <p class="pl-1">or drag and drop</p>
                                        </div>
                                        <p class="text-xs text-gray-500">
                                            PNG, JPG, SVG, or WebP up to 2MB
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Error Message -->
                            <p v-if="formData.errors?.settings?.companyLogo" class="mt-2 text-sm text-red-600">
                                {{ formData.errors.settings.companyLogo }}
                            </p>
                        </div>
                    </div>
                </div>
            </Card>

            <Card title="Booking Terms and Conditions" shadow="sm">
                <p class="text-gray-600 mb-4 dark:text-gray-300">
                    Set your booking terms and conditions that will be displayed to customers during the booking process.
                </p>

                <form @submit.prevent="submit">
                    <div class="mb-4">
                        <InputLabel for="booking-terms" value="Terms and Conditions" />

                        <!-- Quill Editor -->
                        <div class="mt-2">
							<QuillEditor toolbar="minimal" theme="snow" v-model:content="formData.settings.bookingTerms" content-type="html" class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg min-h-[300px]" />
                        </div>
                    </div>

                    <div class="flex justify-end mt-6">
                        <PrimaryButton :disabled="formData.processing" type="submit">
                            Save Settings
                        </PrimaryButton>
                    </div>
                </form>
            </Card>

            <!-- Discount Settings -->
            <Card title="Discount Settings" class="my-6">
                <DiscountSettingsForm :settings="discountSettings" />
            </Card>
        </div>
    </DashboardLayout>
</template>

<style>

</style>
