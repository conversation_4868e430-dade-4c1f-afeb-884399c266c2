<template>
  <div class="space-y-6">
    <!-- Success Message -->
    <div v-if="successMessage" class="p-4 bg-green-50 border border-green-200 rounded-md">
      <p class="text-sm text-green-800">{{ successMessage }}</p>
    </div>

    <!-- Error Message -->
    <div v-if="errors.general" class="p-4 bg-red-50 border border-red-200 rounded-md">
      <p class="text-sm text-red-800">{{ errors.general }}</p>
    </div>

    <div>
      <InputLabel for="discount_application_strategy" value="Discount Application Strategy" />
      <select id="discount_application_strategy" v-model="discountStrategy" class="mt-1 block w-full p-3 border border-gray-300 dark:border-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-accent-primary focus:border-accent-primary dark:focus:ring-accent-primary dark:focus:border-accent-primary sm:text-sm">
        <option value="stack">Stack Discounts</option>
        <option value="single_best">Apply Single Best Discount</option>
      </select>
      <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
        Choose how discounts are applied when multiple are valid.
      </p>
      <InputError :message="errors.discount_application_strategy" class="mt-2" />
    </div>
    <div class="flex items-center justify-end">
      <PrimaryButton :disabled="isLoading" :loading="isLoading" @click="submit">Save Discount Settings</PrimaryButton>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { usePage } from '@inertiajs/vue3';
import axios from 'axios';
import InputError from '@/Components/InputError.vue';
import InputLabel from '@/Components/InputLabel.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';

const props = defineProps({
  settings: Object,
});

const route = window.route;
const page = usePage();
const isLoading = ref(false);
const errors = ref({});
const successMessage = ref('');

const discountStrategy = ref(props.settings?.discount_application_strategy || 'stack');

const submit = async () => {
  try {
    isLoading.value = true;
    errors.value = {};
    successMessage.value = '';

    const response = await axios.post(route('settings.update'), {
      discount_application_strategy: discountStrategy.value
    }, {
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
        'X-Requested-With': 'XMLHttpRequest'
      }
    });

    if (response.data.success) {
      successMessage.value = response.data.success;
    }
  } catch (error) {
    console.error('Error updating discount settings:', error);

    if (error.response && error.response.data.errors) {
      errors.value = error.response.data.errors;
    } else {
      errors.value = { general: error.response?.data?.message || 'An error occurred while updating discount settings.' };
    }
  } finally {
    isLoading.value = false;
  }
};
</script>
