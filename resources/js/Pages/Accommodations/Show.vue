<script setup>
import { ref, onMounted, watch } from 'vue';
import { Head, router, usePage } from '@inertiajs/vue3';
import DashboardLayout from '@/Layouts/DashboardLayout.vue';
import Card from '@/Components/Card.vue';
import PrimaryButton from '@/Components/PrimaryButton.vue';
import Modal from '@/Components/Modal.vue';
import PriceManager from '@/Components/PriceManager.vue';
import axios from 'axios';

// Import the new refactored components
import GalleryManager from '@/Components/Accommodations/GalleryManager.vue';
import AccommodationDetails from '@/Components/Accommodations/AccommodationDetails.vue';
import CalendarManager from '@/Components/Accommodations/CalendarManager.vue';
import UnavailablePeriodsManager from '@/Components/Accommodations/UnavailablePeriodsManager.vue';
import BookingsList from '@/Components/Accommodations/BookingsList.vue';
import DiscountList from '@/Components/Discounts/DiscountList.vue';

const page = usePage();

const props = defineProps({
  accommodation: { type: Object, required: true },
  galleryImages: { type: Array, default: () => [] },
  discounts: { type: Array, default: () => [] },
});

// State variables
const isTogglingPublish = ref(false);
const showDeleteModal = ref(false);
const unavailablePeriods = ref([]);
const loadingUnavailablePeriods = ref(false);

// Fetch unavailable periods
const fetchUnavailablePeriods = async () => {
  loadingUnavailablePeriods.value = true;
  try {
    const response = await axios.get(
      route('accommodations.unavailable-periods.index', props.accommodation.id)
    );
    if (response.data.success) {
      unavailablePeriods.value = response.data.periods;
    }
  } catch (error) {
    console.error('Error fetching unavailable periods:', error);
  } finally {
    loadingUnavailablePeriods.value = false;
  }
};

// Update unavailable periods
const updateUnavailablePeriods = (newPeriods) => {
  unavailablePeriods.value = newPeriods;
};

// Check if accommodation can be published
const publishStatus = ref({ allowed: true, reason: null });

const checkPublishStatus = () => {
  // Check if it has a default price
  const hasDefaultPrice = props.accommodation.prices &&
    props.accommodation.prices.some(price => price.type === 'default' && price.price > 0);

  // Check if the accommodation belongs to a group with a default price
  const hasGroupDefaultPrice = props.accommodation.group &&
    props.accommodation.group.prices &&
    props.accommodation.group.prices.some(price => price.type === 'default' && price.price > 0);

  if (!hasDefaultPrice && !hasGroupDefaultPrice) {
    publishStatus.value = {
      allowed: false,
      reason: 'This accommodation needs a default price before it can be published.'
    };
    return;
  }

  // Check if user has reached their published accommodation limit
  if (props.accommodation.published) {
    publishStatus.value = { allowed: true, reason: null };
    return;
  }

  const subscription = page.props.subscription;
  if (subscription && subscription.features) {
    const accommodationFeature = subscription.features.find(feature => feature.name === 'Accommodations');
    if (accommodationFeature && accommodationFeature.remaining <= 0) {
      publishStatus.value = { 
        allowed: false, 
        reason: `You have reached the maximum number of published accommodations.` 
      };
      return;
    }
  }

  publishStatus.value = { allowed: true, reason: null };
};

// Delete confirmation
const confirmDelete = () => {
  showDeleteModal.value = true;
};

const cancelDelete = () => {
  showDeleteModal.value = false;
};

const deleteAccommodation = () => {
  router.delete(route('accommodations.destroy', props.accommodation.id), {
    onSuccess: () => {
      showDeleteModal.value = false;
    },
  });
};

// Run these checks when the component is mounted
onMounted(() => {
  checkPublishStatus();
  fetchUnavailablePeriods();
});

// Re-run the check when the accommodation changes
watch(() => props.accommodation, () => {
  checkPublishStatus();
}, { deep: true });
</script>

<template>
  <Head :title="accommodation.name" />
  <DashboardLayout :title="accommodation.name">
    <template #header>
      <div class="flex justify-between items-center">
        <h2 class="font-semibold text-xl text-primary leading-tight">{{ accommodation.name }}</h2>
        <PrimaryButton as="Link" :href="route('accommodations.index')" variant="light">Back to Accommodations</PrimaryButton>
      </div>
    </template>

    <div class="py-6 px-4 sm:px-6 lg:px-8">
      <div class="max-w-7xl mx-auto">
        <Card shadow="sm">
          <div class="p-6">
            <div class="flex flex-col md:flex-row">
              <!-- Image Gallery -->
              <div class="w-full md:w-1/3 mb-6 md:mb-0 md:mr-6">
                <GalleryManager :accommodation="accommodation" :initial-images="galleryImages" />
              </div>

              <!-- Details -->
              <div class="w-full md:w-2/3">
                <AccommodationDetails 
                  :accommodation="accommodation" 
                  :publish-status="publishStatus" 
                  :is-toggling-publish="isTogglingPublish"
                  @delete-requested="confirmDelete"
                />
              </div>
            </div>
          </div>
        </Card>

        <!-- Pricing Information -->
        <Card shadow="sm" class="mt-6">
          <template #header>
            <h2 class="text-xl font-semibold">Pricing Information</h2>
          </template>
          <div class="p-4">
            <PriceManager :accommodation-id="accommodation.id" />
          </div>
        </Card>

        <!-- Discount Information -->
        <Card shadow="sm" class="mt-6">
          <template #header>
            <h2 class="text-xl font-semibold">Discount Information</h2>
          </template>
          <div class="p-4">
            <DiscountList :accommodation="accommodation" :discounts="discounts" />
          </div>
        </Card>

        <!-- Booking Section -->
        <Card shadow="sm" class="mt-6">
          <template #header>
            <h2 class="text-xl font-semibold">Availability & Bookings</h2>
          </template>
          <div class="p-4">
            <!-- Calendar Component -->
            <CalendarManager :accommodation="accommodation" :unavailable-periods="unavailablePeriods" />

            <!-- Unavailable Periods Component -->
            <div class="mt-8">
              <UnavailablePeriodsManager 
                :accommodation="accommodation" 
                :unavailable-periods="unavailablePeriods" 
                :loading-unavailable-periods="loadingUnavailablePeriods"
                @periods-updated="updateUnavailablePeriods"
              />
            </div>

            <!-- Bookings List Component -->
            <div class="mt-8">
              <BookingsList :accommodation="accommodation" />
            </div>
          </div>
        </Card>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <Modal :show="showDeleteModal" @close="cancelDelete" title="Delete Accommodation" footer>
      <p class="text-sm text-gray-600 mb-4">
        Are you sure you want to delete this accommodation? Once deleted, all data will be permanently removed.
      </p>

      <template #footer>
        <PrimaryButton variant="light" @click="cancelDelete">Cancel</PrimaryButton>
        <PrimaryButton variant="danger" @click="deleteAccommodation">Delete Accommodation</PrimaryButton>
      </template>
    </Modal>
  </DashboardLayout>
</template>